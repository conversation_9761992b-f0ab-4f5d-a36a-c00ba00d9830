# Methods: Network Analysis of Microbial Communities

## Data Preparation and Quality Control

### OTU Tables and Taxonomic Data
Three separate OTU (Operational Taxonomic Unit) tables were analyzed, representing bacteria (10,283 OTUs across 160 samples), fungi (538 OTUs across 150 samples), and metazoa (250 OTUs across 61 samples). Each dataset included corresponding taxonomic classification and sample metadata. Prior to analysis, OTUs with constant values (all zeros or identical values across samples) were removed to prevent computational artifacts in correlation calculations.

### Sample Overlap Assessment
Due to varying sequencing success rates across taxonomic groups, sample overlap was assessed for each pairwise comparison. Cross-group analyses were performed using only samples present in both datasets: fungi-metazoa (51 common samples), bacteria-fungi (143 common samples), and bacteria-metazoa (60 common samples).

## Correlation Network Construction

### Correlation Analysis
Spearman rank correlation coefficients were calculated between all OTU pairs within each taxonomic group using the `cor()` function in R with pairwise complete observations to handle missing values. Spearman correlation was chosen for its robustness to non-normal distributions and outliers commonly observed in microbiome data.

### Threshold Application
A fixed correlation threshold of |ρ| ≥ 0.6 was applied consistently across all analyses to identify significant associations. This threshold was selected to capture strong correlations while maintaining statistical rigor. Both positive and negative correlations meeting this threshold were retained, though the analysis revealed that no negative correlations exceeded the -0.6 threshold in any dataset.

### Network Construction
Correlation matrices were converted to undirected networks using the `igraph` package in R. Each OTU represented a node, and significant correlations (|ρ| ≥ 0.6) represented weighted edges. Self-loops and duplicate edges were removed to ensure network integrity.

## Network Analysis and Characterization

### Topological Properties
For each network, we calculated standard topological metrics including:
- **Network density**: The proportion of possible edges that are present
- **Degree distribution**: The number of connections per node
- **Betweenness centrality**: A measure of node importance in network connectivity
- **Hub identification**: Nodes with degree > 6 were classified as hubs

### Community Detection
Community structure was identified using multiple algorithms implemented in the `igraph` package:
- **Louvain algorithm**: Optimization of modularity for community detection
- **Walktrap algorithm**: Random walk-based community detection
- **Label propagation**: Iterative label assignment for community identification
- **Infomap algorithm**: Information-theoretic approach to community detection

The algorithm yielding the highest modularity score was selected for final community assignments. Modularity values range from -1 to 1, with higher values indicating stronger community structure.

## Cross-Group Network Analysis

### Inter-Group Correlations
Cross-group correlation networks were constructed by calculating Spearman correlations between OTUs from different taxonomic groups using samples common to both datasets. The same threshold (|ρ| ≥ 0.6) was applied to maintain consistency across analyses.

### Bipartite Network Properties
Cross-group networks were analyzed as bipartite graphs where nodes were colored by taxonomic origin. Community detection was performed to identify mixed-taxonomic modules, and hub nodes were identified separately for each taxonomic group within the cross-group networks.

## Statistical Analysis and Visualization

### Network Comparison
Networks were compared using both raw metrics and normalized values (divided by total OTU count) to account for differences in network size. Statistical significance of differences between networks was assessed using:
- **Kolmogorov-Smirnov tests**: For comparing degree and betweenness centrality distributions
- **Direct comparison**: For density and modularity metrics

### Visualization
All networks were visualized using enhanced graphics with consistent styling:
- **Node sizing**: Proportional to degree centrality using square root scaling
- **Node coloring**: Based on betweenness centrality using viridis color palette
- **Edge styling**: Blue for positive correlations, red for negative correlations, with width proportional to correlation strength
- **Layout algorithms**: Fruchterman-Reingold and Kamada-Kawai layouts for optimal node positioning
- **Community visualization**: Nodes colored by community membership using ColorBrewer palettes

### Statistical Plots
Comprehensive statistical comparisons were generated including:
- **Network metrics comparison**: Bar plots with value annotations and enhanced styling
- **Correlation distribution analysis**: Histograms showing positive vs. negative correlation patterns
- **Community structure analysis**: Dual-axis plots combining community counts with modularity scores
- **Multi-metric overview**: Faceted plots comparing all network properties across taxonomic groups

## Software and Packages

All analyses were performed in R (version 4.4.2) using the following packages:
- **igraph** (1.6.0): Network construction and analysis
- **vegan** (2.6-8): Ecological statistics and diversity measures
- **tidyverse** (2.0.0): Data manipulation and visualization
- **RColorBrewer** (1.1-3): Color palettes for visualization
- **viridis** (0.6.5): Perceptually uniform color scales
- **corrplot** (0.95): Correlation matrix visualization
- **gridExtra** (2.3): Multiple plot arrangements

## Data Output and Reproducibility

### Generated Files
The analysis produced comprehensive outputs including:
- **Network visualizations**: High-resolution PDF files for each network with multiple layout options
- **Hub node data**: CSV files containing centrality metrics for highly connected nodes
- **Community assignments**: CSV files with OTU-to-community mappings
- **Statistical summaries**: Comprehensive comparison tables and test results
- **Summary report**: Detailed text report with key findings and metrics

### Quality Control
All correlation calculations included handling of missing values through pairwise complete observations. Network simplification removed self-loops and multiple edges. Graphics devices were properly managed to prevent file conflicts, with timestamped filenames ensuring reproducibility across multiple runs.

This methodology ensures robust, reproducible network analysis with consistent visualization standards across all taxonomic groups and comparison types.
